const express = require('express');
const authController = require('../controllers/authController');
const mobileAuthController = require('../controllers/mobileAuthController');

const router = express.Router();

// 现有的OAuth登录路由（Web端）
router.get('/huawei/login', authController.huaweiLogin);
router.get('/huawei/callback', authController.huaweiCallback);
router.post('/verify-token', authController.verifyToken);
router.post('/huawei/verify-app-token', authController.verifyAppToken);

// 新增的移动端登录路由（符合接口文档规范）
// 注意：这些路由将通过 /api/v1/auth 基础路径访问
router.post('/huawei-login', mobileAuthController.huaweiLogin);
router.post('/bind-huawei-account', mobileAuthController.bindHuaweiAccount);
router.post('/unbind-huawei-account', mobileAuthController.unbindHuaweiAccount);
router.post('/refresh-token', mobileAuthController.refreshToken);
router.post('/huawei-account-details', mobileAuthController.getHuaweiAccountDetails);

module.exports = router; 