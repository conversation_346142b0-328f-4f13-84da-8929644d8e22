# 物匣应用接口文档

## 项目概述

**项目名称**: 物匣 (Storage-and-boxes)  
**基础URL**: `http://111.231.195.33:3001`  
**API版本**: v1  
**服务类型**: 物品管理应用后端服务  
**支持平台**: HarmonyOS移动端  

---

## 1. 系统监控接口

### 1.1 服务健康检查
- **接口核心功能描述**：检查后端服务运行状态和健康情况，用于监控服务可用性和系统状态
- **接口地址**: `http://111.231.195.33:3001/health`
- **方法**: GET
- **需要登录**: 否
- **请求参数**: 无
- **响应类型**: application/json
- **返回值**：
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00Z",
  "uptime": 3600,
  "environment": "development"
}
```

---

## 2. 用户认证接口

### 2.1 用户登录
- **接口核心功能描述**：用户通过用户名和密码进行身份认证登录，验证用户凭据并返回登录状态
- **接口地址**: `http://111.231.195.33:3001/api/v1/auth/login`
- **方法**: POST
- **需要登录**: 否
- **请求参数**: 
```json
{
  "username": "string",     // 用户名
  "password": "string"      // 密码
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "string",             // JWT访问令牌
    "refreshToken": "string",      // 刷新令牌
    "expiresIn": 7200,            // 过期时间（秒）
    "user": {
      "userId": "string",          // 用户ID
      "username": "string",        // 用户名
      "displayName": "string",     // 显示名称
      "email": "string",           // 邮箱
      "phone": "string",           // 手机号
      "avatar": "string",          // 头像URL
      "createdAt": "2024-01-01T00:00:00Z",       // 创建时间
      "lastLoginAt": "2024-01-01T00:00:00Z"      // 最后登录时间
    }
  }
}
```

### 2.2 用户注册
- **接口核心功能描述**：新用户注册账号，创建用户账户并返回注册结果
- **接口地址**: `http://111.231.195.33:3001/api/v1/auth/register`
- **方法**: POST
- **需要登录**: 否
- **请求参数**: 
```json
{
  "username": "string",     // 用户名
  "password": "string",     // 密码
  "email": "string",        // 邮箱地址（可选）
  "phone": "string"         // 手机号码（可选）
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": "string",          // 用户ID
    "username": "string",        // 用户名
    "email": "string",           // 邮箱
    "phone": "string",           // 手机号
    "createdAt": "2024-01-01T00:00:00Z"      // 创建时间
  }
}
```

### 2.3 华为账号登录
- **接口核心功能描述**：用户通过华为账号服务进行身份认证登录，无需输入用户名和密码。系统验证华为授权令牌，自动创建或绑定用户账号，返回JWT访问令牌和用户信息
- **接口地址**: `http://111.231.195.33:3001/api/v1/auth/huawei-login`
- **方法**: POST
- **需要登录**: 否
- **请求参数**: 
```json
{
  "huaweiAccountId": "string",     // 华为账号ID（UnionID）
  "displayName": "string",         // 显示名称
  "email": "string",               // 邮箱地址（可选）
  "phone": "string",               // 手机号码（可选）
  "authToken": "string",           // 华为授权令牌
  "deviceInfo": {                  // 设备信息
    "deviceId": "string",          // 设备ID
    "deviceModel": "string",       // 设备型号
    "osVersion": "string",         // 系统版本
    "appVersion": "string"         // 应用版本
  }
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "华为账号登录成功",
  "data": {
    "token": "string",             // JWT访问令牌
    "refreshToken": "string",      // 刷新令牌
    "expiresIn": 7200,            // 过期时间（秒）
    "user": {
      "userId": "string",          // 用户ID
      "username": "string",        // 用户名
      "displayName": "string",     // 显示名称
      "email": "string",           // 邮箱
      "phone": "string",           // 手机号
      "avatar": "string",          // 头像URL
      "isFirstLogin": true,        // 是否首次登录
      "createdAt": "2024-01-01T00:00:00Z",       // 创建时间
      "lastLoginAt": "2024-01-01T00:00:00Z"      // 最后登录时间
    },
    "huaweiAccount": {
      "accountId": "string",       // 华为账号ID
      "bindTime": "2024-01-01T00:00:00Z"         // 绑定时间
    }
  }
}
```

### 2.4 华为账号绑定
- **接口核心功能描述**：将已登录用户的账号与华为账号进行绑定关联。验证华为授权令牌有效性，检查华为账号是否已被其他用户绑定，确保一个华为账号只能绑定一个应用账号
- **接口地址**: `http://111.231.195.33:3001/api/v1/auth/bind-huawei-account`
- **方法**: POST
- **需要登录**: 是
- **请求参数**: 
```json
{
  "huaweiAccountId": "string",     // 华为账号ID
  "authToken": "string",           // 华为授权令牌
  "displayName": "string",         // 显示名称
  "email": "string"                // 邮箱地址
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "华为账号绑定成功",
  "data": {
    "bindTime": "2024-01-01T00:00:00Z",          // 绑定时间
    "huaweiAccountId": "string"    // 华为账号ID
  }
}
```

### 2.5 华为账号解绑
- **接口核心功能描述**：解除当前用户账号与华为账号的绑定关系。需要用户输入密码进行身份确认，确保操作安全性
- **接口地址**: `http://111.231.195.33:3001/api/v1/auth/unbind-huawei-account`
- **方法**: POST
- **需要登录**: 是
- **请求参数**: 
```json
{
  "password": "string"             // 用户密码确认
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "华为账号解绑成功",
  "data": {
    "unbindTime": "2024-01-01T00:00:00Z"         // 解绑时间
  }
}
```

### 2.6 刷新访问令牌
- **接口核心功能描述**：使用刷新令牌获取新的访问令牌，延长用户登录状态的有效期
- **接口地址**: `http://111.231.195.33:3001/api/v1/auth/refresh-token`
- **方法**: POST
- **需要登录**: 否
- **请求参数**: 
```json
{
  "refreshToken": "string"         // 刷新令牌
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "令牌刷新成功",
  "data": {
    "token": "string",             // 新的JWT访问令牌
    "refreshToken": "string",      // 新的刷新令牌
    "expiresIn": 7200             // 过期时间（秒）
  }
}
```

### 2.7 重置密码
- **接口核心功能描述**：用户忘记密码时通过邮箱或手机号重置密码，发送验证码并验证后设置新密码
- **接口地址**: `http://111.231.195.33:3001/api/v1/auth/reset-password`
- **方法**: POST
- **需要登录**: 否
- **请求参数**: 
```json
{
  "email": "string",               // 邮箱地址
  "verificationCode": "string",    // 验证码
  "newPassword": "string"          // 新密码
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": {
    "resetTime": "2024-01-01T00:00:00Z"          // 重置时间
  }
}
```

### 2.8 发送验证码
- **接口核心功能描述**：向用户邮箱或手机号发送验证码，用于密码重置或其他需要验证的操作
- **接口地址**: `http://111.231.195.33:3001/api/v1/auth/send-verification-code`
- **方法**: POST
- **需要登录**: 否
- **请求参数**: 
```json
{
  "email": "string",               // 邮箱地址
  "type": "string"                 // 验证码类型：reset_password, register, login
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": {
    "expiresIn": 300               // 验证码有效期（秒）
  }
}
```

---

## 3. 物品管理接口

### 3.1 获取物品列表
- **接口核心功能描述**：获取用户的所有物品信息，支持分页查询和搜索过滤
- **接口地址**: `http://111.231.195.33:3001/api/v1/items`
- **方法**: GET
- **需要登录**: 是
- **请求参数**: 
```
?page=1&limit=20&search=关键词&type=物品类型&location=位置
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "获取物品列表成功",
  "data": {
    "items": [
      {
        "id": "string",            // 物品ID
        "name": "string",          // 物品名称
        "location": "string",      // 存放位置
        "price": 0.00,            // 价格
        "type": "string",          // 物品类型
        "expiryDays": 0,          // 过期天数
        "productionDate": "2024-01-01",  // 生产日期
        "expiryDate": "2024-01-01",      // 过期日期
        "createdAt": "2024-01-01T00:00:00Z",     // 创建时间
        "updatedAt": "2024-01-01T00:00:00Z"      // 更新时间
      }
    ],
    "total": 100,                 // 总数量
    "page": 1,                    // 当前页码
    "limit": 20                   // 每页数量
  }
}
```

### 3.2 添加物品
- **接口核心功能描述**：添加新的物品到用户库存中，记录物品的基本信息和存放位置
- **接口地址**: `http://111.231.195.33:3001/api/v1/items`
- **方法**: POST
- **需要登录**: 是
- **请求参数**: 
```json
{
  "name": "string",               // 物品名称
  "location": "string",           // 存放位置
  "price": 0.00,                 // 价格
  "type": "string",               // 物品类型
  "expiryDays": 0,               // 过期天数
  "productionDate": "2024-01-01", // 生产日期
  "expiryDate": "2024-01-01"     // 过期日期
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "添加物品成功",
  "data": {
    "id": "string",               // 物品ID
    "name": "string",             // 物品名称
    "location": "string",         // 存放位置
    "price": 0.00,               // 价格
    "type": "string",             // 物品类型
    "expiryDays": 0,             // 过期天数
    "productionDate": "2024-01-01",   // 生产日期
    "expiryDate": "2024-01-01",       // 过期日期
    "createdAt": "2024-01-01T00:00:00Z"        // 创建时间
  }
}
```

### 3.3 更新物品
- **接口核心功能描述**：更新指定物品的信息，包括名称、位置、价格、过期时间等
- **接口地址**: `http://111.231.195.33:3001/api/v1/items/{id}`
- **方法**: PUT
- **需要登录**: 是
- **请求参数**: 
```json
{
  "name": "string",               // 物品名称
  "location": "string",           // 存放位置
  "price": 0.00,                 // 价格
  "type": "string",               // 物品类型
  "expiryDays": 0,               // 过期天数
  "productionDate": "2024-01-01", // 生产日期
  "expiryDate": "2024-01-01"     // 过期日期
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "更新物品成功",
  "data": {
    "id": "string",               // 物品ID
    "name": "string",             // 物品名称
    "location": "string",         // 存放位置
    "price": 0.00,               // 价格
    "type": "string",             // 物品类型
    "expiryDays": 0,             // 过期天数
    "productionDate": "2024-01-01",   // 生产日期
    "expiryDate": "2024-01-01",       // 过期日期
    "updatedAt": "2024-01-01T00:00:00Z"        // 更新时间
  }
}
```

### 3.4 删除物品
- **接口核心功能描述**：删除指定的物品，从用户库存中移除该物品记录
- **接口地址**: `http://111.231.195.33:3001/api/v1/items/{id}`
- **方法**: DELETE
- **需要登录**: 是
- **请求参数**: 无
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "删除物品成功",
  "data": {
    "deletedId": "string"         // 已删除的物品ID
  }
}
```

### 3.5 获取物品详情
- **接口核心功能描述**：获取指定物品的详细信息，包括所有属性和状态
- **接口地址**: `http://111.231.195.33:3001/api/v1/items/{id}`
- **方法**: GET
- **需要登录**: 是
- **请求参数**: 无
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "获取物品详情成功",
  "data": {
    "id": "string",               // 物品ID
    "name": "string",             // 物品名称
    "location": "string",         // 存放位置
    "price": 0.00,               // 价格
    "type": "string",             // 物品类型
    "expiryDays": 0,             // 过期天数
    "productionDate": "2024-01-01",   // 生产日期
    "expiryDate": "2024-01-01",       // 过期日期
    "isExpired": false,           // 是否已过期
    "daysUntilExpiry": 30,        // 距离过期天数
    "createdAt": "2024-01-01T00:00:00Z",        // 创建时间
    "updatedAt": "2024-01-01T00:00:00Z"         // 更新时间
  }
}
```

---

## 4. 通知管理接口

### 4.1 获取通知列表
- **接口核心功能描述**：获取用户的通知消息列表，包括过期提醒、系统通知等
- **接口地址**: `http://111.231.195.33:3001/api/v1/notifications`
- **方法**: GET
- **需要登录**: 是
- **请求参数**: 
```
?page=1&limit=20&type=通知类型&isRead=是否已读
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "获取通知列表成功",
  "data": {
    "notifications": [
      {
        "id": "string",           // 通知ID
        "title": "string",        // 通知标题
        "content": "string",      // 通知内容
        "type": "string",         // 通知类型
        "isRead": false,          // 是否已读
        "createdAt": "2024-01-01T00:00:00Z",    // 创建时间
        "itemId": "string"        // 关联物品ID（可选）
      }
    ],
    "total": 50,                  // 总数量
    "unreadCount": 10             // 未读数量
  }
}
```

### 4.2 标记通知为已读
- **接口核心功能描述**：将指定通知标记为已读状态，更新通知的阅读状态
- **接口地址**: `http://111.231.195.33:3001/api/v1/notifications/{id}/read`
- **方法**: PUT
- **需要登录**: 是
- **请求参数**: 无
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "标记已读成功",
  "data": {
    "notificationId": "string",   // 通知ID
    "isRead": true,               // 已读状态
    "readAt": "2024-01-01T00:00:00Z"            // 阅读时间
  }
}
```

### 4.3 删除通知
- **接口核心功能描述**：删除指定的通知消息，从通知列表中移除
- **接口地址**: `http://111.231.195.33:3001/api/v1/notifications/{id}`
- **方法**: DELETE
- **需要登录**: 是
- **请求参数**: 无
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "删除通知成功",
  "data": {
    "deletedId": "string"         // 已删除的通知ID
  }
}
```

---

## 5. 用户设置接口

### 5.1 获取用户信息
- **接口核心功能描述**：获取当前登录用户的详细信息，包括个人资料和偏好设置
- **接口地址**: `http://111.231.195.33:3001/api/v1/user/profile`
- **方法**: GET
- **需要登录**: 是
- **请求参数**: 无
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "userId": "string",           // 用户ID
    "username": "string",         // 用户名
    "displayName": "string",      // 显示名称
    "email": "string",            // 邮箱
    "phone": "string",            // 手机号
    "avatar": "string",           // 头像URL
    "theme": "string",            // 主题设置
    "language": "string",         // 语言设置
    "createdAt": "2024-01-01T00:00:00Z",        // 创建时间
    "lastLoginAt": "2024-01-01T00:00:00Z"       // 最后登录时间
  }
}
```

### 5.2 更新用户信息
- **接口核心功能描述**：更新用户的个人信息，包括显示名称、邮箱、手机号等
- **接口地址**: `http://111.231.195.33:3001/api/v1/user/profile`
- **方法**: PUT
- **需要登录**: 是
- **请求参数**: 
```json
{
  "displayName": "string",        // 显示名称
  "email": "string",              // 邮箱
  "phone": "string",              // 手机号
  "avatar": "string"              // 头像URL
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "userId": "string",           // 用户ID
    "displayName": "string",      // 显示名称
    "email": "string",            // 邮箱
    "phone": "string",            // 手机号
    "avatar": "string",           // 头像URL
    "updatedAt": "2024-01-01T00:00:00Z"         // 更新时间
  }
}
```

### 5.3 更新用户设置
- **接口核心功能描述**：更新用户的偏好设置，包括主题、语言、通知设置等
- **接口地址**: `http://111.231.195.33:3001/api/v1/user/settings`
- **方法**: PUT
- **需要登录**: 是
- **请求参数**: 
```json
{
  "theme": "string",              // 主题设置：light, dark, auto
  "language": "string",           // 语言设置：zh-CN, en-US
  "notificationSettings": {       // 通知设置
    "expiryReminder": true,       // 过期提醒
    "systemNotification": true    // 系统通知
  }
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "更新设置成功",
  "data": {
    "theme": "string",            // 主题设置
    "language": "string",         // 语言设置
    "notificationSettings": {     // 通知设置
      "expiryReminder": true,     // 过期提醒
      "systemNotification": true  // 系统通知
    },
    "updatedAt": "2024-01-01T00:00:00Z"         // 更新时间
  }
}
```

---

## 6. 统计分析接口

### 6.1 获取库存统计
- **接口核心功能描述**：获取用户库存的统计信息，包括总数量、总价值、分类统计等
- **接口地址**: `http://111.231.195.33:3001/api/v1/statistics/inventory`
- **方法**: GET
- **需要登录**: 是
- **请求参数**: 无
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "获取库存统计成功",
  "data": {
    "totalItems": 100,            // 总物品数量
    "totalValue": 5000.00,        // 总价值
    "expiringItems": 5,           // 即将过期物品数量
    "expiredItems": 2,            // 已过期物品数量
    "categoryStats": [            // 分类统计
      {
        "category": "string",     // 分类名称
        "count": 10,              // 数量
        "value": 500.00           // 价值
      }
    ]
  }
}
```

### 6.2 获取过期提醒
- **接口核心功能描述**：获取即将过期和已过期的物品列表，用于提醒用户及时处理
- **接口地址**: `http://111.231.195.33:3001/api/v1/statistics/expiry-alerts`
- **方法**: GET
- **需要登录**: 是
- **请求参数**: 
```
?days=7&includeExpired=true
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "获取过期提醒成功",
  "data": {
    "expiringItems": [            // 即将过期物品
      {
        "id": "string",           // 物品ID
        "name": "string",         // 物品名称
        "expiryDate": "2024-01-01",     // 过期日期
        "daysUntilExpiry": 3      // 距离过期天数
      }
    ],
    "expiredItems": [             // 已过期物品
      {
        "id": "string",           // 物品ID
        "name": "string",         // 物品名称
        "expiryDate": "2024-01-01",     // 过期日期
        "daysExpired": 5          // 过期天数
      }
    ]
  }
}
```

---

## 7. 外部链接接口

### 7.1 用户协议页面
- **接口核心功能描述**：获取用户协议页面的URL地址，用于在应用中显示用户协议内容
- **接口地址**: `https://example.com/user_agreement.html`
- **方法**: GET
- **需要登录**: 否
- **请求参数**: 无
- **响应类型**: text/html
- **返回值**: HTML页面内容

### 7.2 隐私政策页面
- **接口核心功能描述**：获取隐私政策页面的URL地址，用于在应用中显示隐私政策内容
- **接口地址**: `https://example.com/privacy_policy.html`
- **方法**: GET
- **需要登录**: 否
- **请求参数**: 无
- **响应类型**: text/html
- **返回值**: HTML页面内容

### 7.3 华为认证条款页面
- **接口核心功能描述**：获取华为账号认证条款页面的URL地址，用于华为账号登录时的条款展示
- **接口地址**: `https://privacy.consumer.huawei.com/legal/id/authentication-terms.htm?code=CN&language=zh-CN`
- **方法**: GET
- **需要登录**: 否
- **请求参数**: 无
- **响应类型**: text/html
- **返回值**: HTML页面内容

---

## 统一响应格式

### 成功响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 错误响应格式
```json
{
  "code": 错误码,
  "message": "错误描述",
  "data": null
}
```

### 错误码说明
| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | 操作成功 | 请求处理成功 |
| 4001 | 华为账号验证失败 | 华为授权令牌无效或过期 |
| 4002 | 华为账号已绑定其他用户 | 账号绑定冲突 |
| 4003 | 设备异常 | 设备信息异常或存在安全风险 |
| 4004 | 参数错误 | 请求参数格式错误或缺失 |
| 4005 | 频率限制 | 请求过于频繁 |
| 4006 | 未授权访问 | 需要登录或Token无效 |
| 4007 | 密码错误 | 用户密码验证失败 |
| 4008 | 令牌过期 | JWT令牌已过期 |
| 4009 | 令牌无效 | JWT令牌无效 |
| 5001 | 服务器内部错误 | 服务器异常 |

---

## 认证方式

### Bearer Token认证
需要登录的接口使用Bearer Token认证：
```
Authorization: Bearer {token}
```

### 自动令牌刷新
当访问令牌过期时，系统支持自动刷新：

1. 在请求头中提供刷新令牌：
```
X-Refresh-Token: {refreshToken}
```

2. 系统会自动刷新令牌并在响应头中返回新的令牌：
```
X-New-Access-Token: {newToken}
X-New-Refresh-Token: {newRefreshToken}
```

---

## 接口统计

### 接口总数: 25个

| 分类 | 数量 | 接口列表 |
|------|------|----------|
| 系统监控 | 1个 | 健康检查 |
| 用户认证 | 8个 | 登录、注册、华为登录、绑定、解绑、刷新令牌、重置密码、发送验证码 |
| 物品管理 | 5个 | 获取列表、添加、更新、删除、获取详情 |
| 通知管理 | 3个 | 获取列表、标记已读、删除 |
| 用户设置 | 3个 | 获取信息、更新信息、更新设置 |
| 统计分析 | 2个 | 库存统计、过期提醒 |
| 外部链接 | 3个 | 用户协议、隐私政策、华为条款 |

### 认证统计
| 认证类型 | 数量 | 占比 |
|----------|------|------|
| 无需认证 | 8个 | 32% |
| 需要认证 | 17个 | 68% |

### HTTP方法统计
| 方法 | 数量 | 占比 |
|------|------|------|
| GET | 10个 | 40% |
| POST | 8个 | 32% |
| PUT | 5个 | 20% |
| DELETE | 2个 | 8% |

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**维护人员**: AI系统自动生成  
**项目状态**: 生产就绪 