const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const { getDB } = require('../config/database');
const { validateLogin, validateRegister, validateHuaweiLogin } = require('../validators/auth');
const logger = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { error } = validateLogin(req.body);
    if (error) {
      return res.status(400).json({
        code: 4004,
        message: error.details[0].message,
        timestamp: new Date().toISOString()
      });
    }

    const { username, password } = req.body;
    const db = getDB();

    // 查找用户
    const [users] = await db.execute(
      'SELECT id, username, display_name, email, phone, password_hash, avatar_url, created_at FROM users WHERE username = ? AND is_active = true',
      [username]
    );

    if (users.length === 0) {
      return res.status(401).json({
        code: 4007,
        message: '用户名或密码错误',
        timestamp: new Date().toISOString()
      });
    }

    const user = users[0];

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        code: 4007,
        message: '用户名或密码错误',
        timestamp: new Date().toISOString()
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { userId: user.id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      process.env.JWT_SECRET,
      { expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN }
    );

    // 更新最后登录时间
    await db.execute(
      'UPDATE users SET last_login_at = NOW() WHERE id = ?',
      [user.id]
    );

    // 保存会话
    const sessionId = uuidv4();
    const expiresAt = new Date(Date.now() + parseInt(process.env.JWT_EXPIRES_IN) * 1000);
    
    await db.execute(
      'INSERT INTO user_sessions (id, user_id, access_token, refresh_token, expires_at) VALUES (?, ?, ?, ?, ?)',
      [sessionId, user.id, token, refreshToken, expiresAt]
    );

    res.json({
      code: 200,
      message: '登录成功',
      data: {
        token,
        refreshToken,
        expiresIn: parseInt(process.env.JWT_EXPIRES_IN),
        user: {
          userId: user.id,
          username: user.username,
          displayName: user.display_name,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar_url,
          createdAt: user.created_at,
          lastLoginAt: new Date()
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('登录失败:', error);
    res.status(500).json({
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
});

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { error } = validateRegister(req.body);
    if (error) {
      return res.status(400).json({
        code: 4004,
        message: error.details[0].message,
        timestamp: new Date().toISOString()
      });
    }

    const { username, password, email, phone } = req.body;
    const db = getDB();

    // 检查用户名是否已存在
    const [existingUsers] = await db.execute(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({
        code: 4004,
        message: '用户名已存在',
        timestamp: new Date().toISOString()
      });
    }

    // 加密密码
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const userId = uuidv4();
    await db.execute(
      'INSERT INTO users (id, username, password_hash, email, phone, display_name) VALUES (?, ?, ?, ?, ?, ?)',
      [userId, username, passwordHash, email, phone, username]
    );

    res.json({
      code: 200,
      message: '注册成功',
      data: {
        userId,
        username,
        email,
        phone,
        createdAt: new Date()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('注册失败:', error);
    res.status(500).json({
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
});

// 华为账号登录
router.post('/huawei-login', async (req, res) => {
  try {
    const { error } = validateHuaweiLogin(req.body);
    if (error) {
      return res.status(400).json({
        code: 4004,
        message: error.details[0].message,
        timestamp: new Date().toISOString()
      });
    }

    const { huaweiAccountId, displayName, email, phone, authToken, deviceInfo } = req.body;
    const db = getDB();

    // 验证华为授权令牌（这里简化处理，实际应调用华为API验证）
    if (!authToken) {
      return res.status(401).json({
        code: 4001,
        message: '华为账号验证失败',
        timestamp: new Date().toISOString()
      });
    }

    // 查找是否已有绑定的用户
    let [users] = await db.execute(
      'SELECT id, username, display_name, email, phone, avatar_url, created_at FROM users WHERE huawei_union_id = ? AND is_active = true',
      [huaweiAccountId]
    );

    let user;
    let isFirstLogin = false;

    if (users.length === 0) {
      // 首次登录，创建新用户
      const userId = uuidv4();
      const username = `huawei_${huaweiAccountId.slice(-8)}`;
      
      await db.execute(
        'INSERT INTO users (id, username, display_name, email, phone, huawei_union_id) VALUES (?, ?, ?, ?, ?, ?)',
        [userId, username, displayName, email, phone, huaweiAccountId]
      );

      user = {
        id: userId,
        username,
        display_name: displayName,
        email,
        phone,
        avatar_url: null,
        created_at: new Date()
      };
      isFirstLogin = true;
    } else {
      user = users[0];
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { userId: user.id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      process.env.JWT_SECRET,
      { expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN }
    );

    // 更新最后登录时间
    await db.execute(
      'UPDATE users SET last_login_at = NOW() WHERE id = ?',
      [user.id]
    );

    res.json({
      code: 200,
      message: '华为账号登录成功',
      data: {
        token,
        refreshToken,
        expiresIn: parseInt(process.env.JWT_EXPIRES_IN),
        user: {
          userId: user.id,
          username: user.username,
          displayName: user.display_name,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar_url,
          isFirstLogin,
          createdAt: user.created_at,
          lastLoginAt: new Date()
        },
        huaweiAccount: {
          accountId: huaweiAccountId,
          bindTime: user.created_at
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('华为账号登录失败:', error);
    res.status(500).json({
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
});

// 华为账号绑定
router.post('/bind-huawei-account', authenticateToken, async (req, res) => {
  try {
    const { huaweiAccountId, authToken, displayName, email } = req.body;
    const db = getDB();

    // 验证华为授权令牌
    if (!authToken) {
      return res.status(401).json({
        code: 4001,
        message: '华为账号验证失败',
        timestamp: new Date().toISOString()
      });
    }

    // 检查华为账号是否已被其他用户绑定
    const [existingBindings] = await db.execute(
      'SELECT id FROM users WHERE huawei_union_id = ? AND id != ?',
      [huaweiAccountId, req.user.userId]
    );

    if (existingBindings.length > 0) {
      return res.status(400).json({
        code: 4002,
        message: '华为账号已绑定其他用户',
        timestamp: new Date().toISOString()
      });
    }

    // 绑定华为账号
    await db.execute(
      'UPDATE users SET huawei_union_id = ?, display_name = ?, email = ? WHERE id = ?',
      [huaweiAccountId, displayName, email, req.user.userId]
    );

    res.json({
      code: 200,
      message: '华为账号绑定成功',
      data: {
        bindTime: new Date().toISOString(),
        huaweiAccountId
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('华为账号绑定失败:', error);
    res.status(500).json({
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
});

// 华为账号解绑
router.post('/unbind-huawei-account', authenticateToken, async (req, res) => {
  try {
    const { password } = req.body;
    const db = getDB();

    // 获取用户信息验证密码
    const [users] = await db.execute(
      'SELECT password_hash FROM users WHERE id = ?',
      [req.user.userId]
    );

    if (users.length === 0) {
      return res.status(404).json({
        code: 4004,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, users[0].password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        code: 4007,
        message: '密码错误',
        timestamp: new Date().toISOString()
      });
    }

    // 解绑华为账号
    await db.execute(
      'UPDATE users SET huawei_union_id = NULL, huawei_open_id = NULL WHERE id = ?',
      [req.user.userId]
    );

    res.json({
      code: 200,
      message: '华为账号解绑成功',
      data: {
        unbindTime: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('华为账号解绑失败:', error);
    res.status(500).json({
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
});

// 刷新访问令牌
router.post('/refresh-token', async (req, res) => {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(400).json({
        code: 4004,
        message: '刷新令牌不能为空',
        timestamp: new Date().toISOString()
      });
    }

    const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET);
    
    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        code: 4009,
        message: '令牌无效',
        timestamp: new Date().toISOString()
      });
    }

    const db = getDB();
    
    // 验证用户是否存在
    const [users] = await db.execute(
      'SELECT id, username FROM users WHERE id = ? AND is_active = true',
      [decoded.userId]
    );

    if (users.length === 0) {
      return res.status(401).json({
        code: 4006,
        message: '用户不存在或已禁用',
        timestamp: new Date().toISOString()
      });
    }

    // 生成新的令牌
    const newToken = jwt.sign(
      { userId: decoded.userId, username: users[0].username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    const newRefreshToken = jwt.sign(
      { userId: decoded.userId, type: 'refresh' },
      process.env.JWT_SECRET,
      { expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN }
    );

    res.json({
      code: 200,
      message: '令牌刷新成功',
      data: {
        token: newToken,
        refreshToken: newRefreshToken,
        expiresIn: parseInt(process.env.JWT_EXPIRES_IN)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        code: 4008,
        message: '刷新令牌过期',
        timestamp: new Date().toISOString()
      });
    }
    
    logger.error('刷新令牌失败:', error);
    res.status(500).json({
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
});

// 重置密码
router.post('/reset-password', async (req, res) => {
  try {
    const { email, verificationCode, newPassword } = req.body;
    const db = getDB();

    // 这里简化处理，实际应该验证验证码
    if (!verificationCode || verificationCode !== '123456') {
      return res.status(400).json({
        code: 4004,
        message: '验证码错误',
        timestamp: new Date().toISOString()
      });
    }

    // 查找用户
    const [users] = await db.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (users.length === 0) {
      return res.status(404).json({
        code: 4004,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
    }

    // 加密新密码
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await db.execute(
      'UPDATE users SET password_hash = ? WHERE id = ?',
      [passwordHash, users[0].id]
    );

    res.json({
      code: 200,
      message: '密码重置成功',
      data: {
        resetTime: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('重置密码失败:', error);
    res.status(500).json({
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
});

// 发送验证码
router.post('/send-verification-code', async (req, res) => {
  try {
    const { email, type } = req.body;
    
    // 这里简化处理，实际应该发送真实的验证码邮件
    logger.info(`发送验证码到 ${email}，类型: ${type}`);

    res.json({
      code: 200,
      message: '验证码发送成功',
      data: {
        expiresIn: 300 // 5分钟有效期
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('发送验证码失败:', error);
    res.status(500).json({
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;

