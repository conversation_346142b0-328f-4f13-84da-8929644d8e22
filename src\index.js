require('dotenv').config();
const express = require('express');
const cors = require('cors');
const authRoutes = require('./routes/authRoutes');

const app = express();
const PORT = process.env.PORT || 3001;
const BASE_URL = process.env.BASE_URL || `http://111.231.195.33:${PORT}`;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 添加请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/auth', authRoutes);
app.use('/api/v1/auth', authRoutes);

// Home route
app.get('/', (req, res) => {
  res.json({ 
    message: '<PERSON><PERSON><PERSON> Account Login Server',
    environment: process.env.NODE_ENV || 'development',
    baseUrl: BASE_URL,
    version: '1.0.0',
    endpoints: {
      web: {
        login: `${BASE_URL}/auth/huawei/login`,
        callback: `${BASE_URL}/auth/huawei/callback`
      },
      mobile: {
        login: `${BASE_URL}/api/v1/auth/huawei-login`,
        bind: `${BASE_URL}/api/v1/auth/bind-huawei-account`,
        unbind: `${BASE_URL}/api/v1/auth/unbind-huawei-account`,
        refresh: `${BASE_URL}/api/v1/auth/refresh-token`
      }
    }
  });
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    data: null
  });
});

// 全局错误处理中间件
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  
  res.status(500).json({
    code: 5001,
    message: '服务器内部错误',
    data: null
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📍 Server accessible at ${BASE_URL}`);
  console.log(`🔗 Huawei login URL: ${BASE_URL}/auth/huawei/login`);
  console.log(`📱 Mobile API base: ${BASE_URL}/api/v1/auth/`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📊 Health check: ${BASE_URL}/health`);
}); 